// Example Netlify function for high scores
exports.handler = async function(event, context) {
    // Check request method
    if (event.httpMethod === 'POST') {
        try {
            const data = JSON.parse(event.body);
            
            // Here you would save to a database
            // This is just a placeholder
            console.log('Received high score:', data);
            
            return {
                statusCode: 200,
                body: JSON.stringify({ message: 'Score saved successfully' })
            };
        } catch (error) {
            return {
                statusCode: 500,
                body: JSON.stringify({ error: 'Failed to save score' })
            };
        }
    } else if (event.httpMethod === 'GET') {
        // Return top scores
        // This would normally come from a database
        const mockScores = [
            { name: 'Player1', location: 'USA', score: 1500 },
            { name: 'Player2', location: 'Japan', score: 1200 },
            { name: 'Player3', location: 'Brazil', score: 1000 }
        ];
        
        return {
            statusCode: 200,
            body: JSON.stringify(mockScores)
        };
    }
};