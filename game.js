// Game initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM fully loaded");
    
    // Canvas setup
    const canvas = document.getElementById('game-canvas');
    if (!canvas) {
        console.error("Canvas element not found!");
        return;
    }
    
    const ctx = canvas.getContext('2d');
    
    // Game constants
    const GAME_WIDTH = canvas.width;
    const GAME_HEIGHT = canvas.height;
    let endButton;
    let highScore = 0;
    let highScoreName = '';
    let highScoreAddress = '';
    
    // Game state
    let gameStarted = false;
    let gameOver = false;
    let gamePaused = false;
    let finalScore = 0;
    let gameTime = 0;
    let lastTimeUpdate = 0;
    
    // Difficulty settings
    const DIFFICULTY = {
        level: 1,
        maxLevel: 5,
        enemySpeedMultiplier: 1,
        enemySpawnRateMultiplier: 1,
        enemyShootingChance: 0,
        levelUpTime: 30000, // 30 seconds for testing (change to 120000 for 2 minutes)
        lastLevelUpTime: 0
    };
    
    // Player setup
    const player = {
        x: 400,
        y: 700,
        width: 50,
        height: 30,
        speed: 5,
        color: '#4da6ff',
        shielded: false,
        tripleShot: false,
        activePowerUps: []
    };
    
    // Bullets array
    const bullets = [];
    const BULLET_SPEED = 10;
    const BULLET_WIDTH = 10;
    const BULLET_HEIGHT = 10;
    const BULLET_COLOR = '#ff4d4d';
    const BULLET_COOLDOWN = 250; // milliseconds between shots
    let lastShotTime = 0;
    
    // Enemy bullets array
    const enemyBullets = [];
    const ENEMY_BULLET_SPEED = 7;
    const ENEMY_BULLET_WIDTH = 8;
    const ENEMY_BULLET_HEIGHT = 8;
    const ENEMY_BULLET_COLOR = '#ffff00';
    const ENEMY_BULLET_COOLDOWN = 2000; // milliseconds between enemy shots
    
    // Enemies array
    const enemies = [];
    const ENEMY_SPAWN_RATE = 1500; // milliseconds between enemy spawns
    let lastEnemySpawnTime = 0;
    
    // Explosion particles array
    const explosions = [];
    
    // Score
    let score = 0;
    
    // Add to game constants section
    const powerUps = [];
    const POWER_UP_TYPES = {
        SHIELD: {
            name: 'Shield',
            color: '#4287f5',
            duration: 10000, // 10 seconds
            effect: function(player) {
                player.shielded = true;
                console.log("Shield activated"); // Debug log
            },
            endEffect: function(player) {
                player.shielded = false;
                console.log("Shield deactivated"); // Debug log
            }
        },
        RAPID_FIRE: {
            name: 'Rapid Fire',
            color: '#f54242',
            duration: 8000, // 8 seconds
            effect: function() {
                BULLET_COOLDOWN_ORIGINAL = BULLET_COOLDOWN;
                BULLET_COOLDOWN = 50; // Very fast firing
            },
            endEffect: function() {
                BULLET_COOLDOWN = BULLET_COOLDOWN_ORIGINAL;
            }
        },
        TRIPLE_SHOT: {
            name: 'Triple Shot',
            color: '#42f5a7',
            duration: 8000, // 8 seconds
            effect: function() {
                player.tripleShot = true;
            },
            endEffect: function() {
                player.tripleShot = false;
            }
        },
        SLOW_MOTION: {
            name: 'Slow Motion',
            color: '#f5d442',
            duration: 6000, // 6 seconds
            effect: function() {
                ENEMY_SPEED_ORIGINAL = DIFFICULTY.enemySpeedMultiplier;
                DIFFICULTY.enemySpeedMultiplier *= 0.5; // Half speed
            },
            endEffect: function() {
                DIFFICULTY.enemySpeedMultiplier = ENEMY_SPEED_ORIGINAL;
            }
        }
    };

    // Store original values for resetting
    let BULLET_COOLDOWN_ORIGINAL;
    let ENEMY_SPEED_ORIGINAL;
    
    // Create start button
    function createStartButton() {
        const startButton = document.createElement('button');
        startButton.textContent = 'START GAME';
        startButton.style.position = 'absolute';
        startButton.style.top = '50%';
        startButton.style.left = '50%';
        startButton.style.transform = 'translate(-50%, -50%)';
        startButton.style.padding = '15px 30px';
        startButton.style.fontSize = '24px';
        startButton.style.backgroundColor = '#4da6ff';
        startButton.style.color = 'white';
        startButton.style.border = 'none';
        startButton.style.borderRadius = '5px';
        startButton.style.cursor = 'pointer';
        startButton.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.3)';
        startButton.style.zIndex = '1000'; // Ensure it's above other elements
        startButton.id = 'start-button';
        
        // Hover effect
        startButton.onmouseover = function() {
            this.style.backgroundColor = '#2a80d2';
        };
        startButton.onmouseout = function() {
            this.style.backgroundColor = '#4da6ff';
        };
        
        // Add click event with debugging
        startButton.addEventListener('click', function(e) {
            console.log("Start button clicked");
            gameStarted = true;
            startGame();
            
            // Remove button after a short delay to ensure it's processed
            setTimeout(function() {
                const btn = document.getElementById('start-button');
                if (btn) btn.remove();
            }, 50);
        });
        
        // Add touch event with debugging
        startButton.addEventListener('touchstart', function(e) {
            console.log("Start button touched");
            e.preventDefault(); // Prevent default touch behavior
            
            // Set a flag to track if this touch has been processed
            this.touchProcessed = true;
            
            gameStarted = true;
            startGame();
            
            // Remove button after a short delay to ensure it's processed
            setTimeout(function() {
                const btn = document.getElementById('start-button');
                if (btn) btn.remove();
            }, 50);
        }, { passive: false });
        
        // Prevent ghost clicks
        startButton.addEventListener('touchend', function(e) {
            if (this.touchProcessed) {
                e.preventDefault();
                this.touchProcessed = false;
            }
        }, { passive: false });
        
        document.getElementById('game-container').appendChild(startButton);
        console.log("Start button created and added to DOM");
    }
    
    // Create end button
    function createEndButton() {
        endButton = document.createElement('button');
        endButton.textContent = 'END GAME';
        endButton.style.position = 'absolute';
        endButton.style.top = '10px';
        endButton.style.right = '10px';
        endButton.style.padding = '8px 16px';
        endButton.style.fontSize = '16px';
        endButton.style.backgroundColor = '#ff4d4d';
        endButton.style.color = 'white';
        endButton.style.border = 'none';
        endButton.style.borderRadius = '5px';
        endButton.style.cursor = 'pointer';
        endButton.style.display = 'none'; // Hidden initially
        endButton.id = 'end-button';
        
        // Hover effect
        endButton.onmouseover = function() {
            this.style.backgroundColor = '#d43c3c';
        };
        endButton.onmouseout = function() {
            this.style.backgroundColor = '#ff4d4d';
        };
        
        // Add click event
        endButton.addEventListener('click', function() {
            endGame();
            endButton.style.display = 'none';
        });
        
        document.getElementById('game-container').appendChild(endButton);
    }
    
    // Draw welcome screen
    function drawWelcomeScreen() {
        // Background
        ctx.fillStyle = '#1a1a2e';
        ctx.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
        
        // Title
        ctx.fillStyle = 'white';
        ctx.font = '48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('SPACE SHOOTER', GAME_WIDTH / 2, GAME_HEIGHT / 2 - 150);
        
        // Instructions - moved down further from the button position
        ctx.font = '20px Arial';
        ctx.fillText('Arrow Keys: Move', GAME_WIDTH / 2, GAME_HEIGHT / 2 + 100);
        ctx.fillText('Space: Shoot', GAME_WIDTH / 2, GAME_HEIGHT / 2 + 140);
        ctx.fillText('P: Pause', GAME_WIDTH / 2, GAME_HEIGHT / 2 + 180);
        
        // Difficulty warning
        ctx.fillStyle = '#ff6b6b';
        ctx.fillText('Warning: Difficulty increases every 30 seconds!', GAME_WIDTH / 2, GAME_HEIGHT / 2 + 240);
        ctx.fillText('Enemies will shoot back at higher levels!', GAME_WIDTH / 2, GAME_HEIGHT / 2 + 280);
    }
    
    // Keyboard controls
    const keys = {
        ArrowLeft: false,
        ArrowRight: false,
        ArrowUp: false,
        ArrowDown: false,
        ' ': false, // Space bar
        'r': false, // Restart game
        'p': false  // Pause game
    };
    
    // Event listeners for keyboard
    document.addEventListener('keydown', function(e) {
        if (keys.hasOwnProperty(e.key)) {
            keys[e.key] = true;
            e.preventDefault();
        }
        
        // Toggle pause on 'p' key press
        if (e.key === 'p' && gameStarted && !gameOver) {
            togglePause();
        }
    });
    
    document.addEventListener('keyup', function(e) {
        if (keys.hasOwnProperty(e.key)) {
            keys[e.key] = false;
            e.preventDefault();
        }
        
        // Check for restart on game over
        if (gameOver && e.key === 'r') {
            restartGame();
        }
    });
    
    // Toggle pause state
    function togglePause() {
        gamePaused = !gamePaused;
        console.log(gamePaused ? 'Game Paused' : 'Game Resumed');
    }
    
    // Check collision between two objects
    function checkCollision(obj1, obj2) {
        return obj1.x < obj2.x + obj2.width &&
               obj1.x + obj1.width > obj2.x &&
               obj1.y < obj2.y + obj2.height &&
               obj1.y + obj1.height > obj2.y;
    }
    
    // Create explosion effect
    function createExplosion(x, y, size, color) {
        const particleCount = Math.floor(size / 2); // Number of particles based on enemy size
        
        for (let i = 0; i < particleCount; i++) {
            // Random direction
            const angle = Math.random() * Math.PI * 2;
            // Random speed
            const speed = Math.random() * 5 + 2;
            // Random size
            const particleSize = Math.random() * 6 + 2;
            // Random lifetime
            const lifetime = Math.random() * 30 + 20;
            
            explosions.push({
                x: x + size / 2,
                y: y + size / 2,
                size: particleSize,
                speedX: Math.cos(angle) * speed,
                speedY: Math.sin(angle) * speed,
                color: color,
                lifetime: lifetime,
                maxLifetime: lifetime
            });
        }
    }
    
    // Update explosion particles
    function updateExplosions() {
        for (let i = explosions.length - 1; i >= 0; i--) {
            if (gamePaused) continue;
            
            // Update position
            explosions[i].x += explosions[i].speedX;
            explosions[i].y += explosions[i].speedY;
            
            // Decrease lifetime
            explosions[i].lifetime--;
            
            // Remove dead particles
            if (explosions[i].lifetime <= 0) {
                explosions.splice(i, 1);
            }
        }
    }
    
    // Draw explosion particles
    function drawExplosions() {
        for (const particle of explosions) {
            // Fade out based on remaining lifetime
            const alpha = particle.lifetime / particle.maxLifetime;
            ctx.globalAlpha = alpha;
            ctx.fillStyle = particle.color;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
        }
        ctx.globalAlpha = 1.0; // Reset alpha
    }
    
    // Update difficulty based on game time
    function updateDifficulty() {
        if (gamePaused || gameOver) return;
        
        const currentTime = Date.now();
        
        // Update game time
        if (lastTimeUpdate === 0) {
            lastTimeUpdate = currentTime;
        }
        
        gameTime += currentTime - lastTimeUpdate;
        lastTimeUpdate = currentTime;
        
        // Check if it's time to increase difficulty
        if (currentTime - DIFFICULTY.lastLevelUpTime >= DIFFICULTY.levelUpTime && DIFFICULTY.level < DIFFICULTY.maxLevel) {
            DIFFICULTY.level++;
            DIFFICULTY.lastLevelUpTime = currentTime;
            
            // Increase enemy speed and spawn rate
            DIFFICULTY.enemySpeedMultiplier = 1 + (DIFFICULTY.level - 1) * 0.3;
            DIFFICULTY.enemySpawnRateMultiplier = 1 - (DIFFICULTY.level - 1) * 0.15;
            
            // Enable enemy shooting at level 3+
            if (DIFFICULTY.level >= 3) {
                DIFFICULTY.enemyShootingChance = (DIFFICULTY.level - 2) * 0.001;
            }
            
            console.log(`Difficulty increased to level ${DIFFICULTY.level}`);
            
            // Create a level up notification
            showLevelUpMessage();
        }
    }
    
    // Show level up message
    function showLevelUpMessage() {
        const levelUpDiv = document.createElement('div');
        levelUpDiv.textContent = `LEVEL ${DIFFICULTY.level}`;
        levelUpDiv.style.position = 'absolute';
        levelUpDiv.style.top = '50%';
        levelUpDiv.style.left = '50%';
        levelUpDiv.style.transform = 'translate(-50%, -50%)';
        levelUpDiv.style.color = '#ff4d4d';
        levelUpDiv.style.fontSize = '48px';
        levelUpDiv.style.fontWeight = 'bold';
        levelUpDiv.style.textShadow = '0 0 10px #ff4d4d';
        levelUpDiv.style.zIndex = '100';
        levelUpDiv.style.opacity = '1';
        levelUpDiv.style.transition = 'opacity 2s';
        
        document.getElementById('game-container').appendChild(levelUpDiv);
        
        // Fade out and remove after 2 seconds
        setTimeout(() => {
            levelUpDiv.style.opacity = '0';
            setTimeout(() => {
                levelUpDiv.remove();
            }, 2000);
        }, 100);
    }
    
    // Update player position based on keyboard input
    function updatePlayer() {
        if (gameOver || gamePaused) return;
        
        if (keys.ArrowLeft) {
            player.x = Math.max(0, player.x - player.speed);
        }
        if (keys.ArrowRight) {
            player.x = Math.min(GAME_WIDTH - player.width, player.x + player.speed);
        }
        if (keys.ArrowUp) {
            player.y = Math.max(0, player.y - player.speed);
        }
        if (keys.ArrowDown) {
            player.y = Math.min(GAME_HEIGHT - player.height, player.y + player.speed);
        }
        
        // Handle shooting
        if (keys[' ']) {
            tryToShoot();
        }
    }
    
    // Try to shoot a bullet (with cooldown)
    function tryToShoot() {
        if (gameOver || gamePaused) return;
        
        const currentTime = Date.now();
        if (currentTime - lastShotTime >= BULLET_COOLDOWN) {
            if (player.tripleShot) {
                // Create three bullets in a spread pattern
                bullets.push({
                    x: player.x + player.width / 2 - BULLET_WIDTH / 2,
                    y: player.y - BULLET_HEIGHT,
                    width: BULLET_WIDTH,
                    height: BULLET_HEIGHT,
                    speed: BULLET_SPEED,
                    angle: -0.2 // Left bullet
                });
                
                bullets.push({
                    x: player.x + player.width / 2 - BULLET_WIDTH / 2,
                    y: player.y - BULLET_HEIGHT,
                    width: BULLET_WIDTH,
                    height: BULLET_HEIGHT,
                    speed: BULLET_SPEED,
                    angle: 0 // Center bullet
                });
                
                bullets.push({
                    x: player.x + player.width / 2 - BULLET_WIDTH / 2,
                    y: player.y - BULLET_HEIGHT,
                    width: BULLET_WIDTH,
                    height: BULLET_HEIGHT,
                    speed: BULLET_SPEED,
                    angle: 0.2 // Right bullet
                });
            } else {
                // Create a single bullet
                bullets.push({
                    x: player.x + player.width / 2 - BULLET_WIDTH / 2,
                    y: player.y - BULLET_HEIGHT,
                    width: BULLET_WIDTH,
                    height: BULLET_HEIGHT,
                    speed: BULLET_SPEED,
                    angle: 0
                });
            }
            
            lastShotTime = currentTime;
        }
    }
    
    // Enemy tries to shoot
    function enemyTryToShoot(enemy) {
        if (gameOver || gamePaused) return;
        
        // Random chance to shoot based on difficulty
        if (Math.random() < DIFFICULTY.enemyShootingChance) {
            // Create a new bullet at the bottom of the enemy
            enemyBullets.push({
                x: enemy.x + enemy.width / 2 - ENEMY_BULLET_WIDTH / 2,
                y: enemy.y + enemy.height,
                width: ENEMY_BULLET_WIDTH,
                height: ENEMY_BULLET_HEIGHT,
                speed: ENEMY_BULLET_SPEED
            });
        }
    }
    
    // Update bullets position and check for collisions
    function updateBullets() {
        if (gameOver || gamePaused) return;
        
        // Update player bullets
        for (let i = bullets.length - 1; i >= 0; i--) {
            // Move bullet based on angle
            if (bullets[i].angle) {
                bullets[i].x += Math.sin(bullets[i].angle) * bullets[i].speed;
                bullets[i].y -= Math.cos(bullets[i].angle) * bullets[i].speed;
            } else {
                bullets[i].y -= bullets[i].speed;
            }
            
            // Check for collisions with enemies
            let bulletRemoved = false;
            for (let j = enemies.length - 1; j >= 0; j--) {
                if (checkCollision(bullets[i], enemies[j])) {
                    // Remove the bullet
                    bullets.splice(i, 1);
                    bulletRemoved = true;
                    
                    // Reduce enemy health or remove it
                    enemies[j].health -= 1;
                    if (enemies[j].health <= 0) {
                        // Create explosion at enemy position
                        createExplosion(
                            enemies[j].x, 
                            enemies[j].y, 
                            enemies[j].width, 
                            enemies[j].color
                        );
                        
                        // Add score based on enemy size
                        score += Math.floor(enemies[j].width);
                        enemies.splice(j, 1);
                    }
                    
                    break;
                }
            }
            
            // Skip to next bullet if this one was removed
            if (bulletRemoved) continue;
            
            // Remove bullets that go off screen
            if (bullets[i].y + bullets[i].height < 0) {
                bullets.splice(i, 1);
            }
        }
        
        // Update enemy bullets
        for (let i = enemyBullets.length - 1; i >= 0; i--) {
            enemyBullets[i].y += enemyBullets[i].speed;
            
            // Check for collision with player
            if (checkCollision(enemyBullets[i], player)) {
                // If player has shield, don't end game
                if (player.shielded) {
                    // Create small explosion for shield hit
                    createExplosion(
                        enemyBullets[i].x,
                        enemyBullets[i].y,
                        20,
                        '#4287f5' // Shield color
                    );
                    
                    // Remove the bullet
                    enemyBullets.splice(i, 1);
                } else {
                    // Create explosion for player
                    createExplosion(
                        player.x,
                        player.y,
                        player.width * 2,
                        player.color
                    );
                    
                    // Game over
                    endGame();
                    return;
                }
            }
            
            // Remove bullets that go off screen
            if (enemyBullets[i].y > GAME_HEIGHT) {
                enemyBullets.splice(i, 1);
            }
        }
    }
    
    // Spawn and update enemies
    function updateEnemies() {
        if (gameOver || gamePaused) return;
        
        const currentTime = Date.now();
        
        // Spawn new enemies with adjusted spawn rate based on difficulty
        const adjustedSpawnRate = ENEMY_SPAWN_RATE * DIFFICULTY.enemySpawnRateMultiplier;
        if (currentTime - lastEnemySpawnTime >= adjustedSpawnRate) {
            // Random size between 30 and 80
            const size = Math.random() * 50 + 30;
            
            // Random position at the top of the screen
            const x = Math.random() * (GAME_WIDTH - size);
            
            // Random speed between 1 and 3, adjusted by difficulty
            const baseSpeed = Math.random() * 2 + 1;
            const adjustedSpeed = baseSpeed * DIFFICULTY.enemySpeedMultiplier;
            
            // Random color
            const colors = ['#ff6b6b', '#feca57', '#1dd1a1', '#5f27cd', '#ff9ff3'];
            const color = colors[Math.floor(Math.random() * colors.length)];
            
            // Health based on size
            const health = Math.ceil(size / 20);
            
            // Last shot time for this enemy
            const lastShot = 0;
            
            enemies.push({
                x: x,
                y: -size,
                width: size,
                height: size,
                speed: adjustedSpeed,
                color: color,
                health: health,
                lastShot: lastShot
            });
            
            lastEnemySpawnTime = currentTime;
        }
        
        // Update enemy positions
        for (let i = enemies.length - 1; i >= 0; i--) {
            enemies[i].y += enemies[i].speed;
            
            // Try to shoot if enemy is on screen
            if (enemies[i].y > 0 && enemies[i].y < GAME_HEIGHT - 100) {
                enemyTryToShoot(enemies[i]);
            }
            
            // Check for collision with player
            if (checkCollision(enemies[i], player)) {
                // If player has shield, destroy the enemy but don't end game
                if (player.shielded) {
                    // Create explosion for enemy
                    createExplosion(
                        enemies[i].x, 
                        enemies[i].y, 
                        enemies[i].width, 
                        enemies[i].color
                    );
                    
                    // Add score based on enemy size
                    score += Math.floor(enemies[i].width);
                    
                    // Remove the enemy
                    enemies.splice(i, 1);
                    
                    // Create shield hit effect
                    createExplosion(
                        player.x + player.width/2, 
                        player.y + player.height/2, 
                        30, 
                        '#4287f5' // Shield color
                    );
                } else {
                    // Create explosion for player
                    createExplosion(
                        player.x, 
                        player.y, 
                        player.width * 2, 
                        player.color
                    );
                    
                    // Game over
                    endGame();
                    return;
                }
            }
            
            // Remove enemies that go off screen
            if (enemies[i].y > GAME_HEIGHT) {
                enemies.splice(i, 1);
            }
        }
    }
    
    // End the game
    function endGame() {
        gameOver = true;
        finalScore = score;
        console.log('Game Over! Final Score: ' + finalScore);
        
        // Hide end button when game ends
        if (endButton) {
            endButton.style.display = 'none';
        }
        
        // Check if this is a new high score
        if (finalScore > highScore) {
            showHighScoreForm();
        }
    }
    
    // Restart the game
    function restartGame() {
        // Reset game state
        gameOver = false;
        gamePaused = false;
        score = 0;
        gameTime = 0;
        lastTimeUpdate = 0;
        
        // Reset difficulty
        DIFFICULTY.level = 1;
        DIFFICULTY.enemySpeedMultiplier = 1;
        DIFFICULTY.enemySpawnRateMultiplier = 1;
        DIFFICULTY.enemyShootingChance = 0;
        DIFFICULTY.lastLevelUpTime = 0;
        
        // Reset player position and power-ups
        player.x = 400;
        player.y = 700;
        player.shielded = false;
        player.tripleShot = false;
        player.activePowerUps = [];
        
        // Clear bullets, enemies, power-ups and explosions
        bullets.length = 0;
        enemyBullets.length = 0;
        enemies.length = 0;
        powerUps.length = 0;
        explosions.length = 0;
        
        // Reset timers
        lastShotTime = 0;
        lastEnemySpawnTime = 0;
        
        // Show end button
        if (endButton) {
            endButton.style.display = 'block';
        }
    }
    
    // Draw the player
    function drawPlayer() {
        ctx.fillStyle = player.color;
        
        // Draw the fighter jet shape
        ctx.beginPath();
        // Main body
        ctx.moveTo(player.x, player.y + player.height/2);
        ctx.lineTo(player.x + player.width, player.y + player.height/2);
        ctx.lineTo(player.x + player.width - 10, player.y + player.height);
        ctx.lineTo(player.x + 10, player.y + player.height);
        ctx.closePath();
        ctx.fill();
        
        // Wings
        ctx.beginPath();
        ctx.moveTo(player.x + player.width/2 - 15, player.y + player.height/2);
        ctx.lineTo(player.x + player.width/2 - 5, player.y);
        ctx.lineTo(player.x + player.width/2 + 5, player.y);
        ctx.lineTo(player.x + player.width/2 + 15, player.y + player.height/2);
        ctx.closePath();
        ctx.fill();
        
        // Draw shield if active
        if (player.shielded) {
            ctx.strokeStyle = '#4287f5';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(
                player.x + player.width / 2,
                player.y + player.height / 2,
                player.width * 0.8, // Shield size
                0,
                Math.PI * 2
            );
            ctx.stroke();
            
            // Add glow effect
            ctx.strokeStyle = 'rgba(66, 135, 245, 0.5)';
            ctx.lineWidth = 8;
            ctx.stroke();
        }
    }
    
    // Draw bullets
    function drawBullets() {
        ctx.fillStyle = BULLET_COLOR;
        for (const bullet of bullets) {
            ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
        }
    }
    
    // Draw enemies
    function drawEnemies() {
        for (const enemy of enemies) {
            ctx.fillStyle = enemy.color;
            ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
            
            // Draw health bar
            const healthBarWidth = enemy.width;
            const healthBarHeight = 5;
            const healthPercentage = enemy.health / Math.ceil(enemy.width / 20);
            
            // Background (empty health)
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.fillRect(enemy.x, enemy.y - 10, healthBarWidth, healthBarHeight);
            
            // Foreground (current health)
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fillRect(enemy.x, enemy.y - 10, healthBarWidth * healthPercentage, healthBarHeight);
        }
    }
    
    // Draw score
    function drawScore() {
        ctx.fillStyle = 'white';
        ctx.font = '20px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('Score: ' + score, 20, 30);
    }
    
    // Draw pause screen
    function drawPauseScreen() {
        if (!gamePaused) return;
        
        // Semi-transparent overlay
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
        
        // Pause text
        ctx.fillStyle = 'white';
        ctx.font = '48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('PAUSED', GAME_WIDTH / 2, GAME_HEIGHT / 2 - 40);
        
        // Resume instructions
        ctx.font = '24px Arial';
        ctx.fillText('Press P to resume', GAME_WIDTH / 2, GAME_HEIGHT / 2 + 40);
    }
    
    // Draw game over screen
    function drawGameOver() {
        if (!gameOver) return;
        
        // Semi-transparent overlay
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
        
        // Game over text
        ctx.fillStyle = 'white';
        ctx.font = '48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('GAME OVER', GAME_WIDTH / 2, GAME_HEIGHT / 2 - 40);
        
        // Score text
        ctx.font = '32px Arial';
        ctx.fillText('Final Score: ' + finalScore, GAME_WIDTH / 2, GAME_HEIGHT / 2 + 20);
        
        // Restart instructions
        ctx.font = '24px Arial';
        ctx.fillText('Press R to restart', GAME_WIDTH / 2, GAME_HEIGHT / 2 + 80);
    }
    
    // Draw controls info
    function drawControlsInfo() {
        ctx.fillStyle = 'white';
        ctx.font = '16px Arial';
        ctx.textAlign = 'right';
        ctx.fillText('P: Pause | R: Restart | Arrow Keys: Move | Space: Shoot', GAME_WIDTH - 20, 30);
    }
    
    // Clear the canvas
    function clearCanvas() {
        ctx.clearRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
    }
    
    // Add this function to spawn power-ups
    function spawnPowerUp() {
        if (gameOver || gamePaused) return;
        
        // Random chance to spawn (1% chance per frame)
        if (Math.random() < 0.001) {
            // Random position
            const x = Math.random() * (GAME_WIDTH - 30);
            const y = -30; // Start above the screen
            
            // Random power-up type
            const types = Object.keys(POWER_UP_TYPES);
            const type = types[Math.floor(Math.random() * types.length)];
            
            powerUps.push({
                x: x,
                y: y,
                width: 30,
                height: 30,
                speed: 2,
                type: type,
                color: POWER_UP_TYPES[type].color,
                rotation: 0
            });
        }
    }

    // Add this function to update power-ups
    function updatePowerUps() {
        if (gameOver || gamePaused) return;
        
        // Move power-ups down
        for (let i = powerUps.length - 1; i >= 0; i--) {
            powerUps[i].y += powerUps[i].speed;
            powerUps[i].rotation += 0.05; // Rotate for visual effect
            
            // Check for collision with player
            if (checkCollision(powerUps[i], player)) {
                // Apply power-up effect
                activatePowerUp(powerUps[i].type);
                
                // Remove power-up
                powerUps.splice(i, 1);
                continue;
            }
            
            // Remove power-ups that go off screen
            if (powerUps[i].y > GAME_HEIGHT) {
                powerUps.splice(i, 1);
            }
        }
        
        // Update active power-up timers
        for (let i = player.activePowerUps.length - 1; i >= 0; i--) {
            const powerUp = player.activePowerUps[i];
            
            // Decrease time remaining
            powerUp.timeRemaining -= 16; // Approximately 16ms per frame at 60fps
            
            // Check if power-up has expired
            if (powerUp.timeRemaining <= 0) {
                // End power-up effect
                POWER_UP_TYPES[powerUp.type].endEffect(player);
                
                // Remove from active power-ups
                player.activePowerUps.splice(i, 1);
                
                // Show expiration message
                showPowerUpMessage(`${POWER_UP_TYPES[powerUp.type].name} expired!`, '#ff4d4d');
            }
        }
    }

    // Add this function to activate power-ups
    function activatePowerUp(type) {
        // Apply power-up effect
        POWER_UP_TYPES[type].effect(player);
        
        // Check if this power-up is already active
        const existingIndex = player.activePowerUps.findIndex(p => p.type === type);
        
        if (existingIndex >= 0) {
            // Reset the timer if already active
            player.activePowerUps[existingIndex].timeRemaining = POWER_UP_TYPES[type].duration;
        } else {
            // Add to active power-ups
            player.activePowerUps.push({
                type: type,
                timeRemaining: POWER_UP_TYPES[type].duration
            });
        }
        
        // Show activation message
        showPowerUpMessage(`${POWER_UP_TYPES[type].name} activated!`, '#4da6ff');
    }

    // Add this function to show power-up messages
    function showPowerUpMessage(message, color) {
        const messageDiv = document.createElement('div');
        messageDiv.textContent = message;
        messageDiv.style.position = 'absolute';
        messageDiv.style.top = '100px';
        messageDiv.style.left = '50%';
        messageDiv.style.transform = 'translateX(-50%)';
        messageDiv.style.color = color;
        messageDiv.style.fontSize = '24px';
        messageDiv.style.fontWeight = 'bold';
        messageDiv.style.textShadow = '0 0 5px black';
        messageDiv.style.zIndex = '100';
        messageDiv.style.opacity = '1';
        messageDiv.style.transition = 'opacity 2s';
        
        document.getElementById('game-container').appendChild(messageDiv);
        
        // Fade out and remove after 2 seconds
        setTimeout(() => {
            messageDiv.style.opacity = '0';
            setTimeout(() => {
                messageDiv.remove();
            }, 2000);
        }, 100);
    }

    // Add this function to draw power-ups
    function drawPowerUps() {
        for (const powerUp of powerUps) {
            ctx.save(); // Save the current state
            
            // Translate to center of power-up for rotation
            ctx.translate(powerUp.x + powerUp.width / 2, powerUp.y + powerUp.height / 2);
            ctx.rotate(powerUp.rotation);
            
            // Draw power-up
            ctx.fillStyle = powerUp.color;
            ctx.fillRect(-powerUp.width / 2, -powerUp.height / 2, powerUp.width, powerUp.height);
            
            // Draw icon or letter based on type
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // First letter of power-up type
            ctx.fillText(powerUp.type[0], 0, 0);
            
            ctx.restore(); // Restore the state
        }
    }

    // Add this function to draw active power-ups UI
    function drawActivePowerUps() {
        if (player.activePowerUps.length === 0) return;
        
        const startY = 60; // Start below score
        const height = 25;
        const padding = 5;
        
        for (let i = 0; i < player.activePowerUps.length; i++) {
            const powerUp = player.activePowerUps[i];
            const y = startY + (height + padding) * i;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(20, y, 200, height);
            
            // Progress bar
            const progress = powerUp.timeRemaining / POWER_UP_TYPES[powerUp.type].duration;
            ctx.fillStyle = POWER_UP_TYPES[powerUp.type].color;
            ctx.fillRect(20, y, 200 * progress, height);
            
            // Text
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.textBaseline = 'middle';
            ctx.fillText(POWER_UP_TYPES[powerUp.type].name, 25, y + height / 2);
        }
    }
    
    // Main game loop
    function gameLoop() {
        // Clear previous frame
        clearCanvas();
        
        if (!gameStarted) {
            // Draw welcome screen
            drawWelcomeScreen();
            requestAnimationFrame(gameLoop);
            return;
        }
        
        console.log("Game loop running with gameStarted =", gameStarted);
        
        // Update game state
        updatePlayer();
        updateBullets();
        updateEnemies();
        updateExplosions();
        updatePowerUps();
        updateDifficulty();
        
        // Spawn power-ups
        spawnPowerUp();
        
        // Draw everything
        drawEnemies();
        drawPlayer();
        drawBullets();
        drawExplosions();
        drawPowerUps();
        drawActivePowerUps();
        drawScore();
        drawControlsInfo();
        drawVirtualJoystick();
        
        // Draw pause or game over screen if needed
        drawPauseScreen();
        drawGameOver();
        
        // Request next frame
        requestAnimationFrame(gameLoop);
    }
    
    // Start the actual game
    function startGame() {
        console.log("Game starting...");
        
        // Reset game state
        gameOver = false;
        gamePaused = false;
        score = 0;
        gameTime = 0;
        lastTimeUpdate = Date.now();
        
        // Reset difficulty
        DIFFICULTY.level = 1;
        DIFFICULTY.enemySpeedMultiplier = 1;
        DIFFICULTY.enemySpawnRateMultiplier = 1;
        DIFFICULTY.enemyShootingChance = 0;
        DIFFICULTY.lastLevelUpTime = 0;
        
        // Reset player position and power-ups
        player.x = 400;
        player.y = 700;
        player.shielded = false;
        player.tripleShot = false;
        player.activePowerUps = [];
        
        // Clear bullets, enemies, power-ups and explosions
        bullets.length = 0;
        enemyBullets.length = 0;
        enemies.length = 0;
        powerUps.length = 0;
        explosions.length = 0;
        
        // Reset timers
        lastShotTime = 0;
        lastEnemySpawnTime = 0;
        
        // Create touch controls if on a touch device
        if (isTouchDevice) {
            console.log("Creating touch controls for game start");
            // Remove any existing controls first
            const existingShootBtn = document.getElementById('touch-shoot-button');
            const existingPauseBtn = document.getElementById('touch-pause-button');
            if (existingShootBtn) existingShootBtn.remove();
            if (existingPauseBtn) existingPauseBtn.remove();
            
            // Create new controls
            createTouchControls();
        }
        
        // Create end button
        createEndButton();
        
        console.log("Game started successfully");
    }
    
    // Add this function to handle high score submission
    function showHighScoreForm() {
        // Create form container
        const formContainer = document.createElement('div');
        formContainer.style.position = 'absolute';
        formContainer.style.top = '50%';
        formContainer.style.left = '50%';
        formContainer.style.transform = 'translate(-50%, -50%)';
        formContainer.style.backgroundColor = '#1a1a2e';
        formContainer.style.padding = '20px';
        formContainer.style.borderRadius = '10px';
        formContainer.style.boxShadow = '0 0 20px rgba(0, 0, 0, 0.5)';
        formContainer.style.zIndex = '1000';
        formContainer.style.width = '300px';
        formContainer.id = 'high-score-form';
        
        // Create form title
        const title = document.createElement('h2');
        title.textContent = 'NEW HIGH SCORE!';
        title.style.color = '#ff4d4d';
        title.style.textAlign = 'center';
        title.style.marginBottom = '20px';
        
        // Create score display
        const scoreDisplay = document.createElement('p');
        scoreDisplay.textContent = `Your Score: ${finalScore}`;
        scoreDisplay.style.color = 'white';
        scoreDisplay.style.textAlign = 'center';
        scoreDisplay.style.fontSize = '20px';
        scoreDisplay.style.marginBottom = '20px';
        
        // Create name input
        const nameLabel = document.createElement('label');
        nameLabel.textContent = 'Your Name:';
        nameLabel.style.color = 'white';
        nameLabel.style.display = 'block';
        nameLabel.style.marginBottom = '5px';
        
        const nameInput = document.createElement('input');
        nameInput.type = 'text';
        nameInput.style.width = '100%';
        nameInput.style.padding = '8px';
        nameInput.style.marginBottom = '15px';
        nameInput.style.borderRadius = '5px';
        nameInput.style.border = 'none';
        nameInput.id = 'high-score-name';
        
        // Create address input
        const addressLabel = document.createElement('label');
        addressLabel.textContent = 'Your Address:';
        addressLabel.style.color = 'white';
        addressLabel.style.display = 'block';
        addressLabel.style.marginBottom = '5px';
        
        const addressInput = document.createElement('input');
        addressInput.type = 'text';
        addressInput.style.width = '100%';
        addressInput.style.padding = '8px';
        addressInput.style.marginBottom = '15px';
        addressInput.style.borderRadius = '5px';
        addressInput.style.border = 'none';
        addressInput.id = 'high-score-address';
        
        // Create submit button
        const submitButton = document.createElement('button');
        submitButton.textContent = 'SUBMIT';
        submitButton.style.width = '100%';
        submitButton.style.padding = '10px';
        submitButton.style.backgroundColor = '#4da6ff';
        submitButton.style.color = 'white';
        submitButton.style.border = 'none';
        submitButton.style.borderRadius = '5px';
        submitButton.style.cursor = 'pointer';
        submitButton.style.fontSize = '16px';
        
        // Add hover effect
        submitButton.onmouseover = function() {
            this.style.backgroundColor = '#2a80d2';
        };
        submitButton.onmouseout = function() {
            this.style.backgroundColor = '#4da6ff';
        };
        
        // Add submit event
        submitButton.addEventListener('click', function() {
            const name = document.getElementById('high-score-name').value.trim();
            const address = document.getElementById('high-score-address').value.trim();
            
            if (name && address) {
                saveHighScore(name, address, finalScore);
                document.getElementById('high-score-form').remove();
            } else {
                alert('Please enter both your name and address.');
            }
        });
        
        // Assemble form
        formContainer.appendChild(title);
        formContainer.appendChild(scoreDisplay);
        formContainer.appendChild(nameLabel);
        formContainer.appendChild(nameInput);
        formContainer.appendChild(addressLabel);
        formContainer.appendChild(addressInput);
        formContainer.appendChild(submitButton);
        
        document.getElementById('game-container').appendChild(formContainer);
    }

    // Add this function to save high score
    function saveHighScore(name, address, score) {
        // Update local variables
        highScore = score;
        highScoreName = name;
        highScoreAddress = address;
        
        // Save to localStorage
        localStorage.setItem('highScore', score);
        localStorage.setItem('highScoreName', name);
        localStorage.setItem('highScoreAddress', address);
        
        // Update high score display
        updateHighScoreDisplay();
        
        // Optional: Send to server
        // sendHighScoreToServer(name, address, score);
    }

    // Add this function to create high score display
    function createHighScoreDisplay() {
        // Load high score from localStorage
        highScore = parseInt(localStorage.getItem('highScore')) || 0;
        highScoreName = localStorage.getItem('highScoreName') || '';
        highScoreAddress = localStorage.getItem('highScoreAddress') || '';
        
        // Create high score container
        const highScoreContainer = document.createElement('div');
        highScoreContainer.style.position = 'absolute';
        highScoreContainer.style.top = '10px';
        highScoreContainer.style.left = '10px';
        highScoreContainer.style.backgroundColor = 'rgba(26, 26, 46, 0.8)';
        highScoreContainer.style.padding = '10px';
        highScoreContainer.style.borderRadius = '5px';
        highScoreContainer.style.color = 'white';
        highScoreContainer.style.fontSize = '14px';
        highScoreContainer.style.maxWidth = '200px';
        highScoreContainer.id = 'high-score-display';
        
        // Update the display
        updateHighScoreDisplay();
        
        document.getElementById('game-container').appendChild(highScoreContainer);
    }

    // Add this function to update high score display
    function updateHighScoreDisplay() {
        const display = document.getElementById('high-score-display');
        if (display) {
            if (highScore > 0 && highScoreName && highScoreAddress) {
                display.innerHTML = `<strong>HIGH SCORE</strong><br>
                                    ${highScoreName}<br>
                                    ${highScoreAddress}<br>
                                    Score: ${highScore}`;
            } else {
                display.innerHTML = '<strong>HIGH SCORE</strong><br>No records yet';
            }
        }
    }

    // Add this to the initialization section (before gameLoop call)
    createHighScoreDisplay();

    // Optional: Function to send high score to server
    function sendHighScoreToServer(name, address, score) {
        fetch('/api/highscores', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                address: address,
                score: score
            })
        })
        .then(response => response.json())
        .then(data => console.log('Success:', data))
        .catch(error => console.error('Error:', error));
    }
    
    // Initialize
    drawWelcomeScreen();
    createStartButton();
    createEndButton();
    gameLoop();
});

// Add to the initialization section
let isTouchDevice = false;
let touchStartX = 0;
let touchStartY = 0;
let touchJoystickActive = false;
let virtualJoystick = {
    centerX: 0,
    centerY: 0,
    currentX: 0,
    currentY: 0,
    size: 100
};

// Add touch event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Existing code...
    
    // Detect touch device
    isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    // Add touch event listeners if it's a touch device
    if (isTouchDevice) {
        canvas.addEventListener('touchstart', handleTouchStart, false);
        canvas.addEventListener('touchmove', handleTouchMove, false);
        canvas.addEventListener('touchend', handleTouchEnd, false);
        
        // Create shoot button for touch devices
        createTouchShootButton();
        
        // Create pause button for touch devices
        createTouchPauseButton();
    }
    
    // Make canvas responsive
    makeCanvasResponsive();
    window.addEventListener('resize', makeCanvasResponsive);
});

// Make canvas responsive to screen size
function makeCanvasResponsive() {
    const gameContainer = document.getElementById('game-container');
    const containerWidth = gameContainer.clientWidth;
    const containerHeight = gameContainer.clientHeight;
    
    // Calculate the maximum size that fits in the container while maintaining aspect ratio
    const scale = Math.min(
        containerWidth / GAME_WIDTH,
        containerHeight / GAME_HEIGHT
    );
    
    // Apply the scale
    canvas.style.width = (GAME_WIDTH * scale) + 'px';
    canvas.style.height = (GAME_HEIGHT * scale) + 'px';
}

// Create a virtual shoot button for touch devices
function createTouchShootButton() {
    const shootButton = document.createElement('div');
    shootButton.id = 'touch-shoot-button';
    shootButton.textContent = '🔥';
    shootButton.style.position = 'absolute';
    shootButton.style.bottom = '20px';
    shootButton.style.right = '20px';
    shootButton.style.width = '70px';
    shootButton.style.height = '70px';
    shootButton.style.borderRadius = '50%';
    shootButton.style.backgroundColor = 'rgba(255, 100, 100, 0.5)';
    shootButton.style.display = 'flex';
    shootButton.style.justifyContent = 'center';
    shootButton.style.alignItems = 'center';
    shootButton.style.fontSize = '30px';
    shootButton.style.color = 'white';
    shootButton.style.userSelect = 'none';
    shootButton.style.zIndex = '100';
    
    // Add touch events for shooting
    shootButton.addEventListener('touchstart', function(e) {
        e.preventDefault();
        keys[' '] = true;
    });
    
    shootButton.addEventListener('touchend', function(e) {
        e.preventDefault();
        keys[' '] = false;
    });
    
    document.getElementById('game-container').appendChild(shootButton);
}

// Create a pause button for touch devices
function createTouchPauseButton() {
    const pauseButton = document.createElement('div');
    pauseButton.id = 'touch-pause-button';
    pauseButton.textContent = '⏸️';
    pauseButton.style.position = 'absolute';
    pauseButton.style.top = '20px';
    pauseButton.style.right = '20px';
    pauseButton.style.width = '50px';
    pauseButton.style.height = '50px';
    pauseButton.style.borderRadius = '50%';
    pauseButton.style.backgroundColor = 'rgba(100, 100, 255, 0.5)';
    pauseButton.style.display = 'flex';
    pauseButton.style.justifyContent = 'center';
    pauseButton.style.alignItems = 'center';
    pauseButton.style.fontSize = '25px';
    pauseButton.style.color = 'white';
    pauseButton.style.userSelect = 'none';
    pauseButton.style.zIndex = '100';
    
    // Add touch event for pausing
    pauseButton.addEventListener('touchstart', function(e) {
        e.preventDefault();
        togglePause();
    });
    
    document.getElementById('game-container').appendChild(pauseButton);
}

// Handle touch start event
function handleTouchStart(e) {
    e.preventDefault();
    
    // Get touch position relative to canvas
    const touch = e.touches[0];
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    touchStartX = (touch.clientX - rect.left) * scaleX;
    touchStartY = (touch.clientY - rect.top) * scaleY;
    
    // If touch is in the left half of the screen, activate virtual joystick
    if (touchStartX < GAME_WIDTH / 2) {
        touchJoystickActive = true;
        virtualJoystick.centerX = touchStartX;
        virtualJoystick.centerY = touchStartY;
        virtualJoystick.currentX = touchStartX;
        virtualJoystick.currentY = touchStartY;
    }
}

// Handle touch move event
function handleTouchMove(e) {
    e.preventDefault();
    
    if (touchJoystickActive) {
        const touch = e.touches[0];
        const rect = canvas.getBoundingClientRect();
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;
        
        virtualJoystick.currentX = (touch.clientX - rect.left) * scaleX;
        virtualJoystick.currentY = (touch.clientY - rect.top) * scaleY;
        
        // Calculate joystick displacement
        const dx = virtualJoystick.currentX - virtualJoystick.centerX;
        const dy = virtualJoystick.currentY - virtualJoystick.centerY;
        
        // Calculate distance from center
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // Normalize if distance is greater than joystick size
        if (distance > virtualJoystick.size) {
            const angle = Math.atan2(dy, dx);
            virtualJoystick.currentX = virtualJoystick.centerX + Math.cos(angle) * virtualJoystick.size;
            virtualJoystick.currentY = virtualJoystick.centerY + Math.sin(angle) * virtualJoystick.size;
        }
        
        // Update player movement based on joystick position
        updatePlayerFromJoystick();
    }
}

// Handle touch end event
function handleTouchEnd(e) {
    e.preventDefault();
    
    // Reset joystick and movement
    touchJoystickActive = false;
    keys.ArrowLeft = false;
    keys.ArrowRight = false;
    keys.ArrowUp = false;
    keys.ArrowDown = false;
}

// Update player position based on virtual joystick
function updatePlayerFromJoystick() {
    if (!touchJoystickActive) return;
    
    // Calculate joystick displacement
    const dx = virtualJoystick.currentX - virtualJoystick.centerX;
    const dy = virtualJoystick.currentY - virtualJoystick.centerY;
    
    // Reset all movement keys
    keys.ArrowLeft = false;
    keys.ArrowRight = false;
    keys.ArrowUp = false;
    keys.ArrowDown = false;
    
    // Set movement based on joystick position
    if (dx < -20) keys.ArrowLeft = true;
    if (dx > 20) keys.ArrowRight = true;
    if (dy < -20) keys.ArrowUp = true;
    if (dy > 20) keys.ArrowDown = true;
}

// Draw virtual joystick
function drawVirtualJoystick() {
    if (!isTouchDevice || !touchJoystickActive) return;
    
    // Draw joystick base
    ctx.beginPath();
    ctx.arc(virtualJoystick.centerX, virtualJoystick.centerY, virtualJoystick.size, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.fill();
    
    // Draw joystick handle
    ctx.beginPath();
    ctx.arc(virtualJoystick.currentX, virtualJoystick.currentY, virtualJoystick.size / 2, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
    ctx.fill();
}

// Improved start button with better mobile support
function createStartButton() {
    const startButton = document.createElement('button');
    startButton.textContent = 'START GAME';
    startButton.style.position = 'absolute';
    startButton.style.top = '50%';
    startButton.style.left = '50%';
    startButton.style.transform = 'translate(-50%, -50%)';
    startButton.style.padding = '15px 30px';
    startButton.style.fontSize = '24px';
    startButton.style.backgroundColor = '#4da6ff';
    startButton.style.color = 'white';
    startButton.style.border = 'none';
    startButton.style.borderRadius = '5px';
    startButton.style.cursor = 'pointer';
    startButton.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.3)';
    startButton.style.zIndex = '1000'; // Ensure it's above other elements
    startButton.id = 'start-button';
    
    // Hover effect
    startButton.onmouseover = function() {
        this.style.backgroundColor = '#2a80d2';
    };
    startButton.onmouseout = function() {
        this.style.backgroundColor = '#4da6ff';
    };
    
    // Add click event with debugging
    startButton.addEventListener('click', function(e) {
        console.log("Start button clicked");
        gameStarted = true;
        startGame();
        
        // Remove button after a short delay to ensure it's processed
        setTimeout(function() {
            const btn = document.getElementById('start-button');
            if (btn) btn.remove();
        }, 50);
    });
    
    // Add touch event with debugging
    startButton.addEventListener('touchstart', function(e) {
        console.log("Start button touched");
        e.preventDefault(); // Prevent default touch behavior
        
        // Set a flag to track if this touch has been processed
        this.touchProcessed = true;
        
        gameStarted = true;
        startGame();
        
        // Remove button after a short delay to ensure it's processed
        setTimeout(function() {
            const btn = document.getElementById('start-button');
            if (btn) btn.remove();
        }, 50);
    }, { passive: false });
    
    // Prevent ghost clicks
    startButton.addEventListener('touchend', function(e) {
        if (this.touchProcessed) {
            e.preventDefault();
            this.touchProcessed = false;
        }
    }, { passive: false });
    
    document.getElementById('game-container').appendChild(startButton);
    console.log("Start button created and added to DOM");
}

// Improved touch controls implementation
document.addEventListener('DOMContentLoaded', function() {
    // Canvas setup
    const canvas = document.getElementById('game-canvas');
    const ctx = canvas.getContext('2d');
    
    // Detect touch device
    isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    console.log("Touch device detected:", isTouchDevice);
    
    // Create start button
    createStartButton();
    
    // Add touch controls if it's a touch device
    if (isTouchDevice) {
        // Remove any existing touch controls first
        const existingShootBtn = document.getElementById('touch-shoot-button');
        const existingPauseBtn = document.getElementById('touch-pause-button');
        if (existingShootBtn) existingShootBtn.remove();
        if (existingPauseBtn) existingPauseBtn.remove();
        
        // Create new touch controls
        createTouchControls();
        
        // Add touch event listeners to canvas
        canvas.addEventListener('touchstart', handleTouchStart, { passive: false });
        canvas.addEventListener('touchmove', handleTouchMove, { passive: false });
        canvas.addEventListener('touchend', handleTouchEnd, { passive: false });
        
        console.log("Touch controls initialized");
    }
    
    // Start game loop
    gameLoop();
});

// Create all touch controls
function createTouchControls() {
    createTouchShootButton();
    createTouchPauseButton();
    console.log("Touch controls created");
}

// Create a more visible shoot button
function createTouchShootButton() {
    const gameContainer = document.getElementById('game-container');
    
    const shootButton = document.createElement('div');
    shootButton.id = 'touch-shoot-button';
    shootButton.innerHTML = '🔥';
    
    // Style the button to be more visible
    shootButton.style.position = 'absolute';
    shootButton.style.bottom = '30px';
    shootButton.style.right = '30px';
    shootButton.style.width = '80px';
    shootButton.style.height = '80px';
    shootButton.style.borderRadius = '50%';
    shootButton.style.backgroundColor = 'rgba(255, 77, 77, 0.7)';
    shootButton.style.border = '3px solid white';
    shootButton.style.display = 'flex';
    shootButton.style.justifyContent = 'center';
    shootButton.style.alignItems = 'center';
    shootButton.style.fontSize = '40px';
    shootButton.style.color = 'white';
    shootButton.style.zIndex = '1000';
    shootButton.style.userSelect = 'none';
    shootButton.style.touchAction = 'none';
    
    // Add touch events
    shootButton.addEventListener('touchstart', function(e) {
        e.preventDefault();
        keys[' '] = true;
        this.style.backgroundColor = 'rgba(255, 0, 0, 0.9)';
        console.log("Shoot button pressed");
    }, { passive: false });
    
    shootButton.addEventListener('touchend', function(e) {
        e.preventDefault();
        keys[' '] = false;
        this.style.backgroundColor = 'rgba(255, 77, 77, 0.7)';
        console.log("Shoot button released");
    }, { passive: false });
    
    gameContainer.appendChild(shootButton);
    console.log("Shoot button added to DOM");
}

// Create a more visible pause button
function createTouchPauseButton() {
    const gameContainer = document.getElementById('game-container');
    
    const pauseButton = document.createElement('div');
    pauseButton.id = 'touch-pause-button';
    pauseButton.innerHTML = '⏸️';
    
    // Style the button to be more visible
    pauseButton.style.position = 'absolute';
    pauseButton.style.top = '20px';
    pauseButton.style.right = '20px';
    pauseButton.style.width = '60px';
    pauseButton.style.height = '60px';
    pauseButton.style.borderRadius = '50%';
    pauseButton.style.backgroundColor = 'rgba(77, 77, 255, 0.7)';
    pauseButton.style.border = '3px solid white';
    pauseButton.style.display = 'flex';
    pauseButton.style.justifyContent = 'center';
    pauseButton.style.alignItems = 'center';
    pauseButton.style.fontSize = '30px';
    pauseButton.style.color = 'white';
    pauseButton.style.zIndex = '1000';
    pauseButton.style.userSelect = 'none';
    pauseButton.style.touchAction = 'none';
    
    // Add touch event
    pauseButton.addEventListener('touchstart', function(e) {
        e.preventDefault();
        togglePause();
        console.log("Pause button pressed");
    }, { passive: false });
    
    gameContainer.appendChild(pauseButton);
    console.log("Pause button added to DOM");
}

// Improved touch handling
function handleTouchStart(e) {
    e.preventDefault();
    
    // Get touch position relative to canvas
    const touch = e.touches[0];
    const rect = canvas.getBoundingClientRect();
    
    // Calculate the scale factor between canvas coordinates and display coordinates
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    // Convert touch position to canvas coordinates
    touchStartX = (touch.clientX - rect.left) * scaleX;
    touchStartY = (touch.clientY - rect.top) * scaleY;
    
    // If touch is in the left half of the screen, activate virtual joystick
    if (touchStartX < canvas.width / 2) {
        touchJoystickActive = true;
        virtualJoystick.centerX = touchStartX;
        virtualJoystick.centerY = touchStartY;
        virtualJoystick.currentX = touchStartX;
        virtualJoystick.currentY = touchStartY;
        console.log("Joystick activated at", touchStartX, touchStartY);
    }
}

// Improved touch move handling
function handleTouchMove(e) {
    e.preventDefault();
    
    if (touchJoystickActive) {
        const touch = e.touches[0];
        const rect = canvas.getBoundingClientRect();
        
        // Calculate the scale factor
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;
        
        // Convert touch position to canvas coordinates
        const touchX = (touch.clientX - rect.left) * scaleX;
        const touchY = (touch.clientY - rect.top) * scaleY;
        
        virtualJoystick.currentX = touchX;
        virtualJoystick.currentY = touchY;
        
        // Calculate joystick displacement
        const dx = virtualJoystick.currentX - virtualJoystick.centerX;
        const dy = virtualJoystick.currentY - virtualJoystick.centerY;
        
        // Calculate distance from center
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // Normalize if distance is greater than joystick size
        if (distance > virtualJoystick.size) {
            const angle = Math.atan2(dy, dx);
            virtualJoystick.currentX = virtualJoystick.centerX + Math.cos(angle) * virtualJoystick.size;
            virtualJoystick.currentY = virtualJoystick.centerY + Math.sin(angle) * virtualJoystick.size;
        }
        
        // Update player movement based on joystick position
        updatePlayerFromJoystick();
    }
}

// Improved player movement from joystick
function updatePlayerFromJoystick() {
    if (!touchJoystickActive) return;
    
    // Calculate joystick displacement
    const dx = virtualJoystick.currentX - virtualJoystick.centerX;
    const dy = virtualJoystick.currentY - virtualJoystick.centerY;
    
    // Reset all movement keys
    keys.ArrowLeft = false;
    keys.ArrowRight = false;
    keys.ArrowUp = false;
    keys.ArrowDown = false;
    
    // Set movement based on joystick position with lower threshold for better responsiveness
    if (dx < -10) {
        keys.ArrowLeft = true;
        console.log("Moving left");
    }
    if (dx > 10) {
        keys.ArrowRight = true;
        console.log("Moving right");
    }
    if (dy < -10) {
        keys.ArrowUp = true;
        console.log("Moving up");
    }
    if (dy > 10) {
        keys.ArrowDown = true;
        console.log("Moving down");
    }
}

// Draw a more visible virtual joystick
function drawVirtualJoystick() {
    if (!isTouchDevice || !touchJoystickActive) return;
    
    // Draw joystick base (outer circle)
    ctx.beginPath();
    ctx.arc(virtualJoystick.centerX, virtualJoystick.centerY, virtualJoystick.size, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.fill();
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // Draw joystick handle (inner circle)
    ctx.beginPath();
    ctx.arc(virtualJoystick.currentX, virtualJoystick.currentY, virtualJoystick.size / 2, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.fill();
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 3;
    ctx.stroke();
    
    // Draw line connecting center to current position
    ctx.beginPath();
    ctx.moveTo(virtualJoystick.centerX, virtualJoystick.centerY);
    ctx.lineTo(virtualJoystick.currentX, virtualJoystick.currentY);
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();
}

// Add a function to initialize touch controls when the game starts
function startGame() {
    console.log("Game starting...");
    
    // Reset game state
    gameOver = false;
    gamePaused = false;
    score = 0;
    gameTime = 0;
    lastTimeUpdate = Date.now();
    
    // Reset difficulty
    DIFFICULTY.level = 1;
    DIFFICULTY.enemySpeedMultiplier = 1;
    DIFFICULTY.enemySpawnRateMultiplier = 1;
    DIFFICULTY.enemyShootingChance = 0;
    DIFFICULTY.lastLevelUpTime = 0;
    
    // Reset player position and power-ups
    player.x = 400;
    player.y = 700;
    player.shielded = false;
    player.tripleShot = false;
    player.activePowerUps = [];
    
    // Clear bullets, enemies, power-ups and explosions
    bullets.length = 0;
    enemyBullets.length = 0;
    enemies.length = 0;
    powerUps.length = 0;
    explosions.length = 0;
    
    // Reset timers
    lastShotTime = 0;
    lastEnemySpawnTime = 0;
    
    // Create touch controls if on a touch device
    if (isTouchDevice) {
        console.log("Creating touch controls for game start");
        // Remove any existing controls first
        const existingShootBtn = document.getElementById('touch-shoot-button');
        const existingPauseBtn = document.getElementById('touch-pause-button');
        if (existingShootBtn) existingShootBtn.remove();
        if (existingPauseBtn) existingPauseBtn.remove();
        
        // Create new controls
        createTouchControls();
    }
    
    // Create end button
    createEndButton();
    
    console.log("Game started successfully");
}

// Improved gameLoop function
function gameLoop() {
    // Clear previous frame
    clearCanvas();
    
    if (!gameStarted) {
        // Draw welcome screen
        drawWelcomeScreen();
        requestAnimationFrame(gameLoop);
        return;
    }
    
    console.log("Game loop running with gameStarted =", gameStarted);
    
    // Update game state
    updatePlayer();
    updateBullets();
    updateEnemies();
    updateExplosions();
    updatePowerUps();
    updateDifficulty();
    
    // Spawn power-ups
    spawnPowerUp();
    
    // Draw everything
    drawEnemies();
    drawPlayer();
    drawBullets();
    drawExplosions();
    drawPowerUps();
    drawActivePowerUps();
    drawScore();
    drawControlsInfo();
    drawVirtualJoystick();
    
    // Draw pause or game over screen if needed
    drawPauseScreen();
    drawGameOver();
    
    // Request next frame
    requestAnimationFrame(gameLoop);
}

// Add this function to handle high score submission
function showHighScoreForm() {
    // Create form container
    const formContainer = document.createElement('div');
    formContainer.style.position = 'absolute';
    formContainer.style.top = '50%';
    formContainer.style.left = '50%';
    formContainer.style.transform = 'translate(-50%, -50%)';
    formContainer.style.backgroundColor = '#1a1a2e';
    formContainer.style.padding = '20px';
    formContainer.style.borderRadius = '10px';
    formContainer.style.boxShadow = '0 0 20px rgba(0, 0, 0, 0.5)';
    formContainer.style.zIndex = '1000';
    formContainer.style.width = '300px';
    formContainer.id = 'high-score-form';
    
    // Create form title
    const title = document.createElement('h2');
    title.textContent = 'NEW HIGH SCORE!';
    title.style.color = '#ff4d4d';
    title.style.textAlign = 'center';
    title.style.marginBottom = '20px';
    
    // Create score display
    const scoreDisplay = document.createElement('p');
    scoreDisplay.textContent = `Your Score: ${finalScore}`;
    scoreDisplay.style.color = 'white';
    scoreDisplay.style.textAlign = 'center';
    scoreDisplay.style.fontSize = '20px';
    scoreDisplay.style.marginBottom = '20px';
    
    // Create name input
    const nameLabel = document.createElement('label');
    nameLabel.textContent = 'Your Name:';
    nameLabel.style.color = 'white';
    nameLabel.style.display = 'block';
    nameLabel.style.marginBottom = '5px';
    
    const nameInput = document.createElement('input');
    nameInput.type = 'text';
    nameInput.style.width = '100%';
    nameInput.style.padding = '8px';
    nameInput.style.marginBottom = '15px';
    nameInput.style.borderRadius = '5px';
    nameInput.style.border = 'none';
    nameInput.id = 'high-score-name';
    
    // Create address input
    const addressLabel = document.createElement('label');
    addressLabel.textContent = 'Your Address:';
    addressLabel.style.color = 'white';
    addressLabel.style.display = 'block';
    addressLabel.style.marginBottom = '5px';
    
    const addressInput = document.createElement('input');
    addressInput.type = 'text';
    addressInput.style.width = '100%';
    addressInput.style.padding = '8px';
    addressInput.style.marginBottom = '15px';
    addressInput.style.borderRadius = '5px';
    addressInput.style.border = 'none';
    addressInput.id = 'high-score-address';
    
    // Create submit button
    const submitButton = document.createElement('button');
    submitButton.textContent = 'SUBMIT';
    submitButton.style.width = '100%';
    submitButton.style.padding = '10px';
    submitButton.style.backgroundColor = '#4da6ff';
    submitButton.style.color = 'white';
    submitButton.style.border = 'none';
    submitButton.style.borderRadius = '5px';
    submitButton.style.cursor = 'pointer';
    submitButton.style.fontSize = '16px';
    
    // Add hover effect
    submitButton.onmouseover = function() {
        this.style.backgroundColor = '#2a80d2';
    };
    submitButton.onmouseout = function() {
        this.style.backgroundColor = '#4da6ff';
    };
    
    // Add submit event
    submitButton.addEventListener('click', function() {
        const name = document.getElementById('high-score-name').value.trim();
        const address = document.getElementById('high-score-address').value.trim();
        
        if (name && address) {
            saveHighScore(name, address, finalScore);
            document.getElementById('high-score-form').remove();
        } else {
            alert('Please enter both your name and address.');
        }
    });
    
    // Assemble form
    formContainer.appendChild(title);
    formContainer.appendChild(scoreDisplay);
    formContainer.appendChild(nameLabel);
    formContainer.appendChild(nameInput);
    formContainer.appendChild(addressLabel);
    formContainer.appendChild(addressInput);
    formContainer.appendChild(submitButton);
    
    document.getElementById('game-container').appendChild(formContainer);
}

// Add this function to save high score
function saveHighScore(name, address, score) {
    // Update local variables
    highScore = score;
    highScoreName = name;
    high

