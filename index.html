<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Space Shooter</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #121212;
            font-family: Arial, sans-serif;
            touch-action: none;
            overflow: hidden;
        }
        
        .page-container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .ad-container {
            width: 160px;
            height: 600px;
            background-color: rgba(255, 255, 255, 0.1);
            margin: 0 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border: 1px dashed rgba(255, 255, 255, 0.3);
            overflow: hidden;
            position: relative;
        }
        
        .ad-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .ad-image:hover {
            transform: scale(1.05);
        }
        
        .ad-label {
            position: absolute;
            top: 5px;
            right: 5px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        #game-container {
            position: relative;
            width: 800px;
            height: 800px;
            touch-action: none;
        }
        
        #game-canvas {
            background-color: #1a1a2e;
            border: 2px solid #30475e;
            display: block;
            max-width: 100%;
            max-height: 100%;
            touch-action: none;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* Add styles for touch controls */
        #touch-shoot-button, #touch-pause-button {
            -webkit-tap-highlight-color: transparent;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
        }
        
        /* Improve button styles for better touch response */
        button {
            -webkit-appearance: none;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            cursor: pointer;
        }
        
        #start-button {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            user-select: none;
            -webkit-user-select: none;
        }
        
        @media (max-width: 1200px) {
            .page-container {
                flex-direction: column;
                align-items: center;
            }
            
            .ad-container {
                width: 728px;
                height: 90px;
                margin: 20px 0;
                flex-direction: row;
            }
        }
        
        @media (max-width: 768px) {
            #game-container {
                width: 100%;
                height: auto;
                aspect-ratio: 1 / 1;
            }
            
            .ad-container {
                width: 320px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- Left ad container -->
        <div id="left-ad" class="ad-container">
            <span class="ad-label">AD</span>
            <!-- Ad content will be inserted by JavaScript -->
        </div>
        
        <!-- Game container -->
        <div id="game-container">
            <canvas id="game-canvas" width="800" height="800"></canvas>
        </div>
        
        <!-- Right ad container -->
        <div id="right-ad" class="ad-container">
            <span class="ad-label">AD</span>
            <!-- Ad content will be inserted by JavaScript -->
        </div>
    </div>
    
    <!-- Custom ad system script -->
    <script>
        // Ad configuration
        const ads = [
            {
                id: 'ad1',
                name: 'Local Restaurant',
                image: 'ads/restaurant-ad.jpg', // Path to your JPG or GIF ad
                link: 'https://example.com/restaurant',
                position: 'left' // Can be 'left', 'right', or 'both'
            },
            {
                id: 'ad2',
                name: 'Local Gym',
                image: 'ads/gym-ad.gif', // GIF ad
                link: 'https://example.com/gym',
                position: 'right'
            },
            {
                id: 'ad3',
                name: 'Local Bookstore',
                image: 'ads/bookstore-ad.jpg',
                link: 'https://example.com/bookstore',
                position: 'both' // Show on both sides
            }
            // Add more ads as needed
        ];
        
        // Ad click tracking
        const adStats = {
            impressions: {},
            clicks: {}
        };
        
        // Initialize ads
        function initAds() {
            const leftAdContainer = document.getElementById('left-ad');
            const rightAdContainer = document.getElementById('right-ad');
            
            // Filter ads for left position
            const leftAds = ads.filter(ad => ad.position === 'left' || ad.position === 'both');
            if (leftAds.length > 0) {
                // Select random ad for left position
                const randomLeftAd = leftAds[Math.floor(Math.random() * leftAds.length)];
                displayAd(randomLeftAd, leftAdContainer);
            }
            
            // Filter ads for right position
            const rightAds = ads.filter(ad => ad.position === 'right' || ad.position === 'both');
            if (rightAds.length > 0) {
                // Select random ad for right position
                const randomRightAd = rightAds[Math.floor(Math.random() * rightAds.length)];
                displayAd(randomRightAd, rightAdContainer);
            }
            
            // Log initial stats
            console.log('Ad system initialized');
        }
        
        // Display ad in container
        function displayAd(ad, container) {
            // Create ad image element
            const adImage = document.createElement('img');
            adImage.src = ad.image;
            adImage.alt = ad.name;
            adImage.className = 'ad-image';
            adImage.dataset.adId = ad.id;
            
            // Add click event
            adImage.addEventListener('click', function() {
                // Track click
                trackAdClick(ad.id);
                
                // Open link in new tab
                window.open(ad.link, '_blank');
            });
            
            // Clear container and add new ad
            container.innerHTML = '';
            container.appendChild(document.createElement('span')).className = 'ad-label';
            container.querySelector('.ad-label').textContent = 'AD';
            container.appendChild(adImage);
            
            // Track impression
            trackAdImpression(ad.id);
        }
        
        // Track ad impression
        function trackAdImpression(adId) {
            if (!adStats.impressions[adId]) {
                adStats.impressions[adId] = 0;
            }
            adStats.impressions[adId]++;
            
            // Save stats to localStorage
            saveAdStats();
        }
        
        // Track ad click
        function trackAdClick(adId) {
            if (!adStats.clicks[adId]) {
                adStats.clicks[adId] = 0;
            }
            adStats.clicks[adId]++;
            
            console.log(`Ad clicked: ${adId}`);
            
            // Save stats to localStorage
            saveAdStats();
        }
        
        // Save ad stats to localStorage
        function saveAdStats() {
            localStorage.setItem('adStats', JSON.stringify(adStats));
        }
        
        // Load ad stats from localStorage
        function loadAdStats() {
            const savedStats = localStorage.getItem('adStats');
            if (savedStats) {
                adStats = JSON.parse(savedStats);
            }
        }
        
        // Admin function to get ad stats (you can call this from console)
        function getAdStats() {
            return adStats;
        }
        
        // Admin function to reset ad stats (you can call this from console)
        function resetAdStats() {
            adStats.impressions = {};
            adStats.clicks = {};
            saveAdStats();
            console.log('Ad stats reset');
        }
        
        // Rotate ads periodically (every 5 minutes)
        function setupAdRotation() {
            setInterval(initAds, 5 * 60 * 1000);
        }
        
        // Initialize when page loads
        window.addEventListener('load', function() {
            loadAdStats();
            initAds();
            setupAdRotation();
        });
    </script>
    
    <script src="game.js"></script>
</body>
</html>





